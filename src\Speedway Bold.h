// Speedway Bold font from https://damieng.com/zx-origins
#ifndef SPEEDWAY_BOLD_H_
#define SPEEDWAY_BOLD_H_

#include <stdint.h>

static const uint8_t FONT_SPEEDWAY_BOLD_BITMAP[] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //   
	0x0c, 0x0c, 0x18, 0x18, 0x00, 0x30, 0x30, 0x00, // ! 
	0x6c, 0x6c, 0xd8, 0x00, 0x00, 0x00, 0x00, 0x00, // " 
	0x00, 0x36, 0x7e, 0x6c, 0xfc, 0xd8, 0x00, 0x00, // # 
	0x0c, 0x1e, 0x30, 0x3c, 0x0c, 0x78, 0x30, 0x00, // $ 
	0x70, 0xb4, 0xe8, 0x10, 0x2e, 0x5a, 0x1c, 0x00, // % 
	0x30, 0x68, 0x68, 0x72, 0xdc, 0xcc, 0x7a, 0x00, // & 
	0x18, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, // ' 
	0x0c, 0x18, 0x30, 0x30, 0x30, 0x18, 0x0c, 0x00, // ( 
	0x30, 0x18, 0x0c, 0x0c, 0x0c, 0x18, 0x30, 0x00, // ) 
	0x00, 0x18, 0x18, 0x7e, 0x38, 0x6c, 0x00, 0x00, // * 
	0x00, 0x18, 0x18, 0xfe, 0x30, 0x30, 0x00, 0x00, // + 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x30, // , 
	0x00, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x00, 0x00, // - 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, // . 
	0x0c, 0x0c, 0x18, 0x18, 0x30, 0x30, 0x60, 0x00, // / 
	0x3c, 0x66, 0x6e, 0xd6, 0xec, 0xcc, 0x78, 0x00, // 0 
	0x18, 0x38, 0x18, 0x18, 0x30, 0x30, 0x30, 0x00, // 1 
	0x3c, 0x66, 0x0c, 0x18, 0x30, 0x60, 0xfc, 0x00, // 2 
	0x3c, 0x66, 0x06, 0x1c, 0x0c, 0xcc, 0x78, 0x00, // 3 
	0x30, 0x66, 0x6c, 0xcc, 0x7e, 0x18, 0x18, 0x00, // 4 
	0x3e, 0x60, 0x60, 0x7c, 0x0c, 0xcc, 0x78, 0x00, // 5 
	0x3e, 0x60, 0x60, 0x7c, 0xcc, 0xcc, 0x78, 0x00, // 6 
	0x7e, 0x06, 0x0c, 0x0c, 0x18, 0x18, 0x30, 0x00, // 7 
	0x3c, 0x66, 0x66, 0x7c, 0xcc, 0xcc, 0x78, 0x00, // 8 
	0x3c, 0x66, 0x66, 0x7c, 0x0c, 0x0c, 0x78, 0x00, // 9 
	0x00, 0x00, 0x18, 0x18, 0x00, 0x30, 0x30, 0x00, // : 
	0x00, 0x00, 0x18, 0x18, 0x00, 0x30, 0x30, 0x60, // ; 
	0x00, 0x0c, 0x18, 0x30, 0x18, 0x0c, 0x00, 0x00, // < 
	0x00, 0x00, 0x7c, 0x00, 0x7c, 0x00, 0x00, 0x00, // = 
	0x00, 0x30, 0x18, 0x0c, 0x18, 0x30, 0x00, 0x00, // > 
	0x3c, 0x66, 0x06, 0x0c, 0x00, 0x30, 0x30, 0x00, // ? 
	0x3e, 0x63, 0x6f, 0x76, 0xde, 0xc0, 0x78, 0x00, // @ 
	0x1c, 0x36, 0x66, 0x66, 0xfc, 0xcc, 0xcc, 0x00, // A 
	0x7c, 0x66, 0x66, 0x7c, 0xcc, 0xcc, 0xf8, 0x00, // B 
	0x3e, 0x66, 0x60, 0x60, 0xc0, 0xcc, 0xf8, 0x00, // C 
	0x7c, 0x66, 0x66, 0x66, 0xcc, 0xcc, 0xf8, 0x00, // D 
	0x7e, 0x60, 0x60, 0x78, 0xc0, 0xc0, 0xfc, 0x00, // E 
	0x7e, 0x60, 0x60, 0x78, 0xc0, 0xc0, 0xc0, 0x00, // F 
	0x3e, 0x60, 0x60, 0x6e, 0xcc, 0xcc, 0xf8, 0x00, // G 
	0x66, 0x66, 0x66, 0x7c, 0xcc, 0xcc, 0xcc, 0x00, // H 
	0x1c, 0x18, 0x18, 0x18, 0x30, 0x30, 0x70, 0x00, // I 
	0x06, 0x06, 0x06, 0x0c, 0x0c, 0x0c, 0x78, 0x00, // J 
	0x66, 0x6c, 0x78, 0x70, 0xd8, 0xcc, 0xc6, 0x00, // K 
	0x38, 0x30, 0x30, 0x30, 0x60, 0x60, 0x7e, 0x00, // L 
	0x7e, 0x6b, 0x6b, 0x6b, 0xd6, 0xd6, 0xd6, 0x00, // M 
	0x7c, 0x66, 0x66, 0x66, 0xcc, 0xcc, 0xcc, 0x00, // N 
	0x3e, 0x66, 0x66, 0x6c, 0xcc, 0xcc, 0xf8, 0x00, // O 
	0x7c, 0x66, 0x66, 0x6c, 0xc0, 0xc0, 0xc0, 0x00, // P 
	0x3c, 0x66, 0x66, 0xc6, 0xdc, 0xcc, 0x76, 0x00, // Q 
	0x7c, 0x66, 0x66, 0x7c, 0xd8, 0xcc, 0xc6, 0x00, // R 
	0x3e, 0x60, 0x60, 0x38, 0x0c, 0x0c, 0xf8, 0x00, // S 
	0x7e, 0x18, 0x18, 0x30, 0x30, 0x60, 0x60, 0x00, // T 
	0x66, 0x66, 0x66, 0xcc, 0xcc, 0xcc, 0x78, 0x00, // U 
	0x66, 0x66, 0x6c, 0x6c, 0x78, 0x70, 0x60, 0x00, // V 
	0x6b, 0x6b, 0x6b, 0xd6, 0xd6, 0xd6, 0x7e, 0x00, // W 
	0x66, 0x66, 0x6c, 0x38, 0x6c, 0xcc, 0xcc, 0x00, // X 
	0xc6, 0xc6, 0x6c, 0x3c, 0x18, 0x30, 0x60, 0x00, // Y 
	0x7c, 0x0c, 0x18, 0x30, 0x60, 0xc0, 0xfc, 0x00, // Z 
	0x1e, 0x18, 0x18, 0x18, 0x30, 0x30, 0x3c, 0x00, // [ 
	0x60, 0x60, 0x30, 0x30, 0x18, 0x18, 0x0c, 0x00, // \ (backslash) 
	0x3c, 0x0c, 0x0c, 0x18, 0x18, 0x18, 0x78, 0x00, // ] 
	0x10, 0x38, 0x6c, 0xc6, 0x00, 0x00, 0x00, 0x00, // ^ 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, // _ 
	0x3c, 0x66, 0x60, 0x78, 0xc0, 0xc0, 0xfc, 0x00, // £ 
	0x00, 0x00, 0x3e, 0x06, 0x7c, 0xcc, 0xfc, 0x00, // a 
	0x30, 0x60, 0x7e, 0x66, 0xcc, 0xcc, 0xfc, 0x00, // b 
	0x00, 0x00, 0x7e, 0x66, 0x60, 0xcc, 0xfc, 0x00, // c 
	0x03, 0x06, 0x7e, 0x66, 0xcc, 0xcc, 0xfc, 0x00, // d 
	0x00, 0x00, 0x7e, 0x66, 0x7c, 0xc0, 0xfc, 0x00, // e 
	0x1e, 0x18, 0x7c, 0x30, 0x60, 0x60, 0xe0, 0x00, // f 
	0x00, 0x00, 0x7e, 0xce, 0xcc, 0xfc, 0x18, 0xf8, // g 
	0x30, 0x60, 0x7e, 0x66, 0xcc, 0xcc, 0xcc, 0x00, // h 
	0x0c, 0x00, 0x18, 0x18, 0x30, 0x30, 0x30, 0x00, // i 
	0x06, 0x00, 0x0c, 0x0c, 0x18, 0x18, 0x18, 0x70, // j 
	0x60, 0x66, 0x6c, 0x78, 0xcc, 0xc6, 0xc6, 0x00, // k 
	0x1c, 0x0c, 0x18, 0x18, 0x18, 0x30, 0x30, 0x00, // l 
	0x00, 0x00, 0x7e, 0x6b, 0x6b, 0xd6, 0xd6, 0x00, // m 
	0x00, 0x00, 0x7c, 0x66, 0x66, 0xcc, 0xcc, 0x00, // n 
	0x00, 0x00, 0x7e, 0x66, 0x66, 0xcc, 0xfc, 0x00, // o 
	0x00, 0x00, 0x7e, 0x66, 0x66, 0xfc, 0xc0, 0xc0, // p 
	0x00, 0x00, 0x7e, 0xc6, 0xcc, 0xfc, 0x18, 0x18, // q 
	0x00, 0x00, 0x7e, 0x66, 0x60, 0xc0, 0xc0, 0x00, // r 
	0x00, 0x00, 0x3e, 0x60, 0x7c, 0x0c, 0xf8, 0x00, // s 
	0x18, 0x18, 0x7c, 0x30, 0x60, 0x60, 0x7c, 0x00, // t 
	0x00, 0x00, 0x66, 0x66, 0x66, 0xcc, 0xfc, 0x00, // u 
	0x00, 0x00, 0x66, 0x66, 0x6c, 0x78, 0x70, 0x00, // v 
	0x00, 0x00, 0x6b, 0x6b, 0xd6, 0xd6, 0x7e, 0x00, // w 
	0x00, 0x00, 0x66, 0x6c, 0x38, 0x6c, 0xcc, 0x00, // x 
	0x00, 0x00, 0x66, 0xc6, 0xcc, 0xfc, 0x18, 0xf8, // y 
	0x00, 0x00, 0x3e, 0x0c, 0x18, 0x30, 0x7c, 0x00, // z 
	0x1c, 0x18, 0x30, 0x60, 0x30, 0x30, 0x38, 0x00, // { 
	0x0c, 0x0c, 0x18, 0x18, 0x30, 0x30, 0x30, 0x00, // | 
	0x1c, 0x0c, 0x0c, 0x06, 0x0c, 0x18, 0x38, 0x00, // } 
	0x76, 0xdc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ~ 
	0x1e, 0x23, 0x5d, 0xb5, 0xb1, 0xba, 0xc4, 0x78, // © 
};

#endif
