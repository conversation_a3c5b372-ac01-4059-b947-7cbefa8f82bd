// Pitstop font from https://damieng.com/zx-origins
#ifndef PITSTOP_H_
#define PITSTOP_H_

#include <stdint.h>

static const uint8_t FONT_PITSTOP_BITMAP[] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //   
	0x18, 0x18, 0x18, 0x28, 0x38, 0x00, 0x18, 0x00, // ! 
	0x66, 0x66, 0xcc, 0x00, 0x00, 0x00, 0x00, 0x00, // " 
	0x6c, 0xde, 0x6c, 0x6c, 0x6c, 0xfe, 0x6c, 0x00, // # 
	0x08, 0x3e, 0x58, 0x3c, 0x16, 0x7c, 0x10, 0x00, // $ 
	0x22, 0x56, 0x2c, 0x18, 0x34, 0x6e, 0x44, 0x00, // % 
	0x38, 0x5c, 0x6c, 0x38, 0x6e, 0x64, 0x3e, 0x00, // & 
	0x18, 0x18, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, // ' 
	0x1c, 0x28, 0x70, 0x60, 0x60, 0x30, 0x1c, 0x00, // ( 
	0x70, 0x28, 0x1c, 0x0c, 0x0c, 0x18, 0x70, 0x00, // ) 
	0x00, 0x6c, 0x38, 0x6c, 0x38, 0x6c, 0x00, 0x00, // * 
	0x18, 0x18, 0x18, 0xfe, 0x28, 0x18, 0x18, 0x00, // + 
	0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x30, 0x00, // , 
	0x00, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x00, 0x00, // - 
	0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x00, 0x00, // . 
	0x00, 0x06, 0x0a, 0x14, 0x28, 0x50, 0x60, 0x00, // / 
	0x3c, 0x66, 0x66, 0x5e, 0x66, 0x66, 0x3c, 0x00, // 0 
	0x18, 0x28, 0x38, 0x18, 0x18, 0x18, 0x18, 0x00, // 1 
	0x7c, 0x0a, 0x06, 0x3c, 0x60, 0x60, 0x3e, 0x00, // 2 
	0x7c, 0x0a, 0x06, 0x7c, 0x06, 0x06, 0x7c, 0x00, // 3 
	0x60, 0x60, 0x6c, 0x6c, 0x36, 0x0c, 0x0c, 0x00, // 4 
	0x3e, 0x50, 0x60, 0x3c, 0x06, 0x66, 0x3c, 0x00, // 5 
	0x3e, 0x60, 0x60, 0x5c, 0x66, 0x66, 0x3c, 0x00, // 6 
	0x7c, 0x06, 0x06, 0x1a, 0x06, 0x06, 0x06, 0x00, // 7 
	0x3c, 0x56, 0x66, 0x3c, 0x66, 0x66, 0x3c, 0x00, // 8 
	0x3c, 0x66, 0x66, 0x3a, 0x06, 0x06, 0x7c, 0x00, // 9 
	0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x00, 0x00, // : 
	0x00, 0x18, 0x18, 0x00, 0x18, 0x18, 0x30, 0x00, // ; 
	0x00, 0x0c, 0x38, 0xe0, 0x38, 0x0c, 0x00, 0x00, // < 
	0x00, 0x00, 0xfe, 0x00, 0xfe, 0x00, 0x00, 0x00, // = 
	0x00, 0x60, 0x38, 0x0e, 0x38, 0x60, 0x00, 0x00, // > 
	0x7c, 0x0a, 0x06, 0x0c, 0x18, 0x00, 0x18, 0x00, // ? 
	0x7c, 0xa6, 0xde, 0xe6, 0xde, 0xc0, 0x7e, 0x00, // @ 
	0x3c, 0x66, 0x66, 0x56, 0x7e, 0x66, 0x66, 0x00, // A 
	0x7c, 0x66, 0x66, 0x5c, 0x66, 0x66, 0x7c, 0x00, // B 
	0x3e, 0x50, 0x60, 0x60, 0x60, 0x60, 0x3e, 0x00, // C 
	0x7c, 0x56, 0x66, 0x66, 0x66, 0x66, 0x7c, 0x00, // D 
	0x3e, 0x60, 0x60, 0x5c, 0x60, 0x60, 0x3e, 0x00, // E 
	0x3e, 0x60, 0x60, 0x5c, 0x60, 0x60, 0x60, 0x00, // F 
	0x3e, 0x60, 0x60, 0x6e, 0x66, 0x6a, 0x3e, 0x00, // G 
	0x66, 0x66, 0x66, 0x5e, 0x66, 0x66, 0x66, 0x00, // H 
	0x7e, 0x14, 0x18, 0x18, 0x18, 0x18, 0x7e, 0x00, // I 
	0x06, 0x06, 0x06, 0x06, 0x06, 0x0a, 0x7c, 0x00, // J 
	0x66, 0x66, 0x6c, 0x58, 0x6c, 0x66, 0x66, 0x00, // K 
	0x60, 0x60, 0x60, 0x60, 0x60, 0x50, 0x3e, 0x00, // L 
	0x6c, 0xbe, 0xfe, 0xd6, 0xc6, 0xc6, 0xc6, 0x00, // M 
	0x66, 0x56, 0x7e, 0x7e, 0x6e, 0x66, 0x66, 0x00, // N 
	0x3c, 0x6a, 0x66, 0x66, 0x66, 0x66, 0x3c, 0x00, // O 
	0x7c, 0x66, 0x66, 0x5c, 0x60, 0x60, 0x60, 0x00, // P 
	0x3c, 0x66, 0x66, 0x66, 0x64, 0x6a, 0x36, 0x00, // Q 
	0x7c, 0x66, 0x66, 0x5c, 0x66, 0x66, 0x66, 0x00, // R 
	0x3e, 0x50, 0x60, 0x3c, 0x06, 0x06, 0x7c, 0x00, // S 
	0x7e, 0x14, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // T 
	0x66, 0x66, 0x66, 0x66, 0x66, 0x56, 0x3c, 0x00, // U 
	0x66, 0x66, 0x66, 0x66, 0x56, 0x3c, 0x18, 0x00, // V 
	0xc6, 0xc6, 0xc6, 0xd6, 0xbe, 0xee, 0xc6, 0x00, // W 
	0x66, 0x66, 0x56, 0x3c, 0x66, 0x66, 0x66, 0x00, // X 
	0x66, 0x66, 0x56, 0x3c, 0x18, 0x18, 0x18, 0x00, // Y 
	0x7e, 0x0a, 0x0c, 0x18, 0x30, 0x60, 0x7e, 0x00, // Z 
	0x0e, 0x14, 0x18, 0x18, 0x18, 0x18, 0x0e, 0x00, // [ 
	0x00, 0x60, 0x50, 0x28, 0x14, 0x0a, 0x06, 0x00, // \ (backslash) 
	0x70, 0x28, 0x18, 0x18, 0x18, 0x18, 0x70, 0x00, // ] 
	0x18, 0x2c, 0x76, 0x18, 0x18, 0x18, 0x18, 0x00, // ^ 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, // _ 
	0x1e, 0x30, 0x30, 0x6c, 0x30, 0x30, 0x7e, 0x00, // £ 
	0x00, 0x00, 0x3c, 0x06, 0x3e, 0x6a, 0x3e, 0x00, // a 
	0x00, 0x60, 0x60, 0x5c, 0x66, 0x66, 0x7c, 0x00, // b 
	0x00, 0x00, 0x3e, 0x50, 0x60, 0x60, 0x3e, 0x00, // c 
	0x00, 0x06, 0x06, 0x3a, 0x66, 0x66, 0x3e, 0x00, // d 
	0x00, 0x00, 0x3c, 0x66, 0x5e, 0x60, 0x3e, 0x00, // e 
	0x00, 0x0e, 0x18, 0x6e, 0x18, 0x18, 0x18, 0x00, // f 
	0x00, 0x00, 0x3e, 0x66, 0x3a, 0x06, 0x3c, 0x00, // g 
	0x00, 0x60, 0x60, 0x5c, 0x66, 0x66, 0x66, 0x00, // h 
	0x00, 0x18, 0x00, 0x38, 0x28, 0x18, 0x18, 0x00, // i 
	0x00, 0x06, 0x00, 0x0e, 0x0a, 0x06, 0x7c, 0x00, // j 
	0x00, 0x60, 0x66, 0x66, 0x5c, 0x66, 0x66, 0x00, // k 
	0x00, 0x30, 0x30, 0x30, 0x30, 0x28, 0x1c, 0x00, // l 
	0x00, 0x00, 0x6c, 0xbe, 0xd6, 0xd6, 0xd6, 0x00, // m 
	0x00, 0x00, 0x7c, 0x56, 0x66, 0x66, 0x66, 0x00, // n 
	0x00, 0x00, 0x3c, 0x56, 0x66, 0x66, 0x3c, 0x00, // o 
	0x00, 0x00, 0x7c, 0x66, 0x66, 0x5c, 0x60, 0x60, // p 
	0x00, 0x00, 0x3e, 0x66, 0x66, 0x3a, 0x06, 0x06, // q 
	0x00, 0x00, 0x7c, 0x56, 0x60, 0x60, 0x60, 0x00, // r 
	0x00, 0x00, 0x3e, 0x50, 0x3c, 0x06, 0x7c, 0x00, // s 
	0x00, 0x18, 0x76, 0x18, 0x18, 0x18, 0x0e, 0x00, // t 
	0x00, 0x00, 0x66, 0x66, 0x66, 0x56, 0x3c, 0x00, // u 
	0x00, 0x00, 0x66, 0x66, 0x56, 0x3c, 0x18, 0x00, // v 
	0x00, 0x00, 0xc6, 0xd6, 0xd6, 0xbe, 0x6c, 0x00, // w 
	0x00, 0x00, 0x66, 0x56, 0x3c, 0x66, 0x66, 0x00, // x 
	0x00, 0x00, 0x66, 0x66, 0x3a, 0x06, 0x7c, 0x00, // y 
	0x00, 0x00, 0x7e, 0x0a, 0x3c, 0x60, 0x7e, 0x00, // z 
	0x0e, 0x14, 0x18, 0x70, 0x18, 0x18, 0x0e, 0x00, // { 
	0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x00, // | 
	0x70, 0x28, 0x18, 0x0e, 0x18, 0x18, 0x70, 0x00, // } 
	0x36, 0x5e, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, // ~ 
	0x7c, 0x82, 0xba, 0xa2, 0xa2, 0xba, 0x82, 0x7c, // © 
};

#endif
